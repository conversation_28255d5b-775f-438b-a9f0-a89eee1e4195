import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Sparkles } from 'lucide-react'

interface Node {
  x: number
  y: number
  vx: number
  vy: number
  id: number
  originalX: number
  originalY: number
  targetX: number
  targetY: number
  size: number
  pulsePhase: number
  hoverDistance: number
}

interface InteractiveBackgroundProps {
  className?: string
}

export default function InteractiveBackground({ className = '' }: InteractiveBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const nodesRef = useRef<Node[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(true)
  const [isVisible, setIsVisible] = useState(true)
  const lastFrameTime = useRef(0)
  const targetFPS = 30 // Reduced from 60fps for better performance

  // Page Visibility API to pause animation when tab is not active
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden)
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const updateSize = () => {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
      setDimensions({ width: rect.width, height: rect.height })
    }

    updateSize()
    window.addEventListener('resize', updateSize)

    // Initialize nodes with enhanced properties
    const nodeCount = Math.floor((dimensions.width * dimensions.height) / 20000) // Reduced for cleaner look
    nodesRef.current = Array.from({ length: Math.max(nodeCount, 15) }, (_, i) => {
      const x = Math.random() * dimensions.width
      const y = Math.random() * dimensions.height
      return {
        x,
        y,
        originalX: x,
        originalY: y,
        targetX: x,
        targetY: y,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 2 + 1,
        pulsePhase: Math.random() * Math.PI * 2,
        hoverDistance: Math.random() * 50 + 80,
        id: i
      }
    })

    // Mouse tracking
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect()
      mouseRef.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      }
    }

    canvas.addEventListener('mousemove', handleMouseMove)

    // Optimized animation loop with frame rate limiting
    const animate = (currentTime: number) => {
      // Skip animation if not visible or disabled
      if (!isVisible || !isAnimationEnabled) {
        animationRef.current = requestAnimationFrame(animate)
        return
      }

      // Frame rate limiting
      const deltaTime = currentTime - lastFrameTime.current
      const frameInterval = 1000 / targetFPS

      if (deltaTime < frameInterval) {
        animationRef.current = requestAnimationFrame(animate)
        return
      }

      lastFrameTime.current = currentTime - (deltaTime % frameInterval)

      ctx.clearRect(0, 0, dimensions.width, dimensions.height)

      const nodes = nodesRef.current
      const mouse = mouseRef.current

      // Update node positions with enhanced mouse interactions
      nodes.forEach((node) => {
        // Validate node properties
        if (!isFinite(node.x) || !isFinite(node.y) || !isFinite(node.originalX) || !isFinite(node.originalY)) {
          // Reset invalid nodes
          node.x = node.originalX = Math.random() * dimensions.width
          node.y = node.originalY = Math.random() * dimensions.height
          node.targetX = node.x
          node.targetY = node.y
          return
        }

        // Mouse repulsion effect
        if (mouse.x !== -1 && mouse.y !== -1 && isFinite(mouse.x) && isFinite(mouse.y)) {
          const dx = node.x - mouse.x
          const dy = node.y - mouse.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance > 0 && distance < node.hoverDistance && isFinite(distance)) {
            const force = (node.hoverDistance - distance) / node.hoverDistance
            const repelX = (dx / distance) * force * 25
            const repelY = (dy / distance) * force * 25

            if (isFinite(repelX) && isFinite(repelY)) {
              node.targetX = node.originalX + repelX
              node.targetY = node.originalY + repelY
            } else {
              node.targetX = node.originalX
              node.targetY = node.originalY
            }
          } else {
            node.targetX = node.originalX
            node.targetY = node.originalY
          }
        } else {
          node.targetX = node.originalX
          node.targetY = node.originalY
        }

        // Smooth movement towards target with easing
        if (isFinite(node.targetX) && isFinite(node.targetY)) {
          node.x += (node.targetX - node.x) * 0.08
          node.y += (node.targetY - node.y) * 0.08
        }

        // Update original position with gentle drift
        if (isFinite(node.vx) && isFinite(node.vy)) {
          node.originalX += node.vx
          node.originalY += node.vy
        }

        // Bounce off edges
        if (node.originalX <= 0 || node.originalX >= dimensions.width) node.vx *= -1
        if (node.originalY <= 0 || node.originalY >= dimensions.height) node.vy *= -1

        // Keep nodes in bounds
        node.originalX = Math.max(0, Math.min(dimensions.width, node.originalX))
        node.originalY = Math.max(0, Math.min(dimensions.height, node.originalY))

        // Update pulse phase for animation
        node.pulsePhase += 0.015
      })

      // Draw enhanced connections with gradients
      ctx.lineWidth = 1.5
      ctx.globalCompositeOperation = 'screen'

      nodes.forEach((nodeA, i) => {
        nodes.slice(i + 1).forEach((nodeB) => {
          const dx = nodeA.x - nodeB.x
          const dy = nodeA.y - nodeB.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 140) {
            const opacity = (140 - distance) / 140 * 0.25

            // Create gradient for connection
            const gradient = ctx.createLinearGradient(nodeA.x, nodeA.y, nodeB.x, nodeB.y)
            gradient.addColorStop(0, `rgba(99, 102, 241, ${opacity})`)
            gradient.addColorStop(0.5, `rgba(139, 92, 246, ${opacity * 0.8})`)
            gradient.addColorStop(1, `rgba(59, 130, 246, ${opacity})`)

            ctx.strokeStyle = gradient
            ctx.beginPath()
            ctx.moveTo(nodeA.x, nodeA.y)
            ctx.lineTo(nodeB.x, nodeB.y)
            ctx.stroke()
          }
        })

        // Draw enhanced connections to mouse with glow effect
        if (mouse.x !== -1 && mouse.y !== -1) {
          const mouseDistance = Math.sqrt(
            (nodeA.x - mouse.x) ** 2 + (nodeA.y - mouse.y) ** 2
          )
          if (mouseDistance < 180) {
            const opacity = (180 - mouseDistance) / 180 * 0.5

            // Create gradient for mouse connection
            const gradient = ctx.createLinearGradient(nodeA.x, nodeA.y, mouse.x, mouse.y)
            gradient.addColorStop(0, `rgba(99, 102, 241, ${opacity})`)
            gradient.addColorStop(0.5, `rgba(168, 85, 247, ${opacity * 1.2})`)
            gradient.addColorStop(1, `rgba(236, 72, 153, ${opacity * 0.8})`)

            ctx.strokeStyle = gradient
            ctx.lineWidth = 2
            ctx.beginPath()
            ctx.moveTo(nodeA.x, nodeA.y)
            ctx.lineTo(mouse.x, mouse.y)
            ctx.stroke()
            ctx.lineWidth = 1.5
          }
        }
      })

      ctx.globalCompositeOperation = 'source-over'

      // Draw enhanced nodes with pulsing gradients
      nodes.forEach((node) => {
        // Validate node position and size
        if (!isFinite(node.x) || !isFinite(node.y) || !isFinite(node.size)) {
          return // Skip invalid nodes
        }

        const pulseSize = Math.max(0.5, node.size + Math.sin(node.pulsePhase) * 0.4)
        const pulseOpacity = Math.max(0.1, Math.min(1, 0.6 + Math.sin(node.pulsePhase) * 0.2))
        const gradientRadius = Math.max(1, pulseSize * 3)

        // Create radial gradient for each node with validation
        const gradient = ctx.createRadialGradient(
          node.x, node.y, 0,
          node.x, node.y, gradientRadius
        )
        gradient.addColorStop(0, `rgba(99, 102, 241, ${pulseOpacity})`)
        gradient.addColorStop(0.4, `rgba(139, 92, 246, ${pulseOpacity * 0.6})`)
        gradient.addColorStop(0.8, `rgba(59, 130, 246, ${pulseOpacity * 0.3})`)
        gradient.addColorStop(1, 'rgba(59, 130, 246, 0)')

        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2)
        ctx.fill()

        // Add inner glow
        const innerGlowOpacity = Math.max(0, Math.min(1, pulseOpacity * 0.3))
        ctx.fillStyle = `rgba(255, 255, 255, ${innerGlowOpacity})`
        ctx.beginPath()
        ctx.arc(node.x, node.y, pulseSize * 0.4, 0, Math.PI * 2)
        ctx.fill()
      })

      // Draw enhanced mouse cursor glow
      if (mouse.x !== -1 && mouse.y !== -1 && isFinite(mouse.x) && isFinite(mouse.y)) {
        const gradient = ctx.createRadialGradient(
          mouse.x, mouse.y, 0,
          mouse.x, mouse.y, 40
        )
        gradient.addColorStop(0, 'rgba(168, 85, 247, 0.3)')
        gradient.addColorStop(0.5, 'rgba(99, 102, 241, 0.2)')
        gradient.addColorStop(1, 'rgba(59, 130, 246, 0)')

        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.arc(mouse.x, mouse.y, 40, 0, Math.PI * 2)
        ctx.fill()
      }

      animationRef.current = requestAnimationFrame(animate)
    }

    animate(0)

    return () => {
      window.removeEventListener('resize', updateSize)
      canvas.removeEventListener('mousemove', handleMouseMove)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [dimensions.width, dimensions.height, isVisible, isAnimationEnabled])

  return (
    <>
      <canvas
        ref={canvasRef}
        className={`fixed inset-0 w-full h-full pointer-events-none z-0 ${className}`}
      />

      {/* Animation Toggle Button */}
      <Button
        onClick={() => setIsAnimationEnabled(!isAnimationEnabled)}
        size="sm"
        variant="outline"
        className="fixed top-4 right-4 z-50 bg-background/80 backdrop-blur-sm border-border/50 hover:bg-background/90 transition-all duration-200"
        title={isAnimationEnabled ? "Disable background animation" : "Enable background animation"}
      >
        <Sparkles className={`w-4 h-4 ${isAnimationEnabled ? 'text-primary' : 'text-muted-foreground'}`} />
      </Button>
    </>
  )
}
