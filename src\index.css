@tailwind base;
@tailwind components;
@tailwind utilities;

/* CJ <PERSON>ba Portfolio Design System */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 215 25% 15%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;

    --radius: 0.75rem;

    /* Portfolio specific colors */
    --portfolio-accent: 217 91% 60%;
    --portfolio-accent-foreground: 0 0% 100%;
    --portfolio-background: 0 0% 100%;
    --portfolio-surface: 210 40% 98%;
    --portfolio-border: 214 32% 91%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(230 80% 70%));
    --gradient-subtle: linear-gradient(135deg, hsl(210 40% 98%), hsl(214 32% 96%));
    
    /* Shadows */
    --shadow-soft: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-medium: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    --shadow-large: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);

    /* Custom Scrollbar Colors */
    --scrollbar-track: hsl(210 40% 98%);
    --scrollbar-thumb: hsl(214 32% 91%);
    --scrollbar-thumb-hover: hsl(217 91% 60%);
  }

  .dark {
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;

    --card: 222 84% 5%;
    --card-foreground: 210 40% 98%;

    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 5%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 91% 60%;
    --accent-foreground: 222 84% 5%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 217 91% 60%;

    /* Portfolio specific dark colors */
    --portfolio-accent: 217 91% 60%;
    --portfolio-accent-foreground: 222 84% 5%;
    --portfolio-background: 222 84% 5%;
    --portfolio-surface: 217 33% 17%;
    --portfolio-border: 217 33% 17%;
    
    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(230 80% 70%));
    --gradient-subtle: linear-gradient(135deg, hsl(217 33% 17%), hsl(222 84% 5%));
    
    /* Dark shadows */
    --shadow-soft: 0 1px 3px 0 hsl(0 0% 0% / 0.3), 0 1px 2px -1px hsl(0 0% 0% / 0.3);
    --shadow-medium: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -2px hsl(0 0% 0% / 0.3);
    --shadow-large: 0 20px 25px -5px hsl(0 0% 0% / 0.3), 0 8px 10px -6px hsl(0 0% 0% / 0.3);

    /* Dark mode scrollbar colors */
    --scrollbar-track: hsl(222 84% 5%);
    --scrollbar-thumb: hsl(217 33% 17%);
    --scrollbar-thumb-hover: hsl(217 91% 60%);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Premium gradient background with subtle noise texture */
    background:
      /* Fine grain noise texture */
      radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.02) 1px, transparent 0),
      /* Large-scale subtle gradient */
      radial-gradient(ellipse 120% 80% at 50% 0%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
      radial-gradient(ellipse 100% 60% at 80% 100%, rgba(139, 92, 246, 0.02) 0%, transparent 50%),
      radial-gradient(ellipse 80% 100% at 20% 50%, rgba(59, 130, 246, 0.015) 0%, transparent 50%),
      /* Base background */
      hsl(var(--background));
    background-size:
      24px 24px,  /* Noise texture */
      100% 100%,  /* Gradients cover full area */
      100% 100%,
      100% 100%,
      100% 100%;
    background-attachment: fixed;
  }

  /* Dark mode premium background */
  .dark body {
    background:
      /* Fine grain noise texture for dark mode */
      radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.03) 1px, transparent 0),
      /* Large-scale subtle gradient for dark mode */
      radial-gradient(ellipse 120% 80% at 50% 0%, rgba(99, 102, 241, 0.04) 0%, transparent 50%),
      radial-gradient(ellipse 100% 60% at 80% 100%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
      radial-gradient(ellipse 80% 100% at 20% 50%, rgba(59, 130, 246, 0.025) 0%, transparent 50%),
      /* Base dark background */
      hsl(var(--background));
    background-size:
      24px 24px,  /* Noise texture */
      100% 100%,  /* Gradients cover full area */
      100% 100%,
      100% 100%,
      100% 100%;
    background-attachment: fixed;
  }

  /* Premium Custom Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: content-box;
    transition: all 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
    background-clip: content-box;
    transform: scale(1.1);
  }

  ::-webkit-scrollbar-corner {
    background: var(--scrollbar-track);
  }

  /* Enhanced scrollbar for main content areas */
  .scrollbar-enhanced::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scrollbar-enhanced::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--scrollbar-thumb), var(--scrollbar-thumb-hover));
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
    box-shadow: inset 0 0 0 1px hsl(var(--border));
  }

  .scrollbar-enhanced::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-primary);
    box-shadow: inset 0 0 0 1px hsl(var(--primary));
  }

  /* Minimal scrollbar for sidebar and small containers */
  .scrollbar-minimal::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 3px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
    opacity: 1;
  }

  .scrollbar-minimal::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }

  .scrollbar-enhanced {
    scrollbar-width: auto;
    scrollbar-color: var(--scrollbar-thumb-hover) var(--scrollbar-track);
  }

  .scrollbar-minimal {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) transparent;
  }

  .scrollbar-hidden {
    scrollbar-width: none;
  }
}

/* Additional utility classes for scrollbar management */
@layer utilities {
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .scroll-auto {
    scroll-behavior: auto;
  }

  /* Custom Soft Shadow System */
  .shadow-soft-sm {
    box-shadow: 0 1px 2px 0 hsl(0 0% 0% / 0.05), 0 1px 3px 0 hsl(0 0% 0% / 0.1);
  }

  .shadow-soft {
    box-shadow: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-md {
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-xl {
    box-shadow: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-2xl {
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  }

  /* Dark mode soft shadows */
  .dark .shadow-soft-sm {
    box-shadow: 0 1px 2px 0 hsl(0 0% 0% / 0.2), 0 1px 3px 0 hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft {
    box-shadow: 0 1px 3px 0 hsl(0 0% 0% / 0.3), 0 1px 2px -1px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-md {
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -2px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.3), 0 4px 6px -4px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-xl {
    box-shadow: 0 20px 25px -5px hsl(0 0% 0% / 0.3), 0 8px 10px -6px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-2xl {
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.5);
  }

  /* Colored soft shadows for premium effects */
  .shadow-soft-primary {
    box-shadow: 0 10px 15px -3px hsl(217 91% 60% / 0.1), 0 4px 6px -4px hsl(217 91% 60% / 0.1);
  }

  .shadow-soft-primary-lg {
    box-shadow: 0 20px 25px -5px hsl(217 91% 60% / 0.15), 0 8px 10px -6px hsl(217 91% 60% / 0.1);
  }

  /* Micro-interactions with 0.3s transitions */
  .transition-micro {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-micro-fast {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-micro-slow {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Terminal-specific scrollbar styles */
  .terminal-scrollbar-dark::-webkit-scrollbar {
    width: 8px;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 4px;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-thumb {
    background: rgba(100, 116, 139, 0.5);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
  }

  .terminal-scrollbar-light::-webkit-scrollbar {
    width: 8px;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 4px;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.7);
  }

  /* Premium Sidebar Scrollbar Styles */
  .scrollbar-sidebar::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-sidebar::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-sidebar::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .scrollbar-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.6);
    transform: scaleX(1.2);
  }

  /* Dark mode sidebar scrollbar */
  .dark .scrollbar-sidebar::-webkit-scrollbar-thumb {
    background: rgba(100, 116, 139, 0.4);
  }

  .dark .scrollbar-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
  }
}