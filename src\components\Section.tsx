import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface SectionProps {
  children: React.ReactNode
  className?: string
  title?: string
  subtitle?: string
  centered?: boolean
  animate?: boolean
}

export default function Section({ 
  children, 
  className, 
  title, 
  subtitle, 
  centered = false,
  animate = true 
}: SectionProps) {
  const content = (
    <section className={cn(
      "mb-24 md:mb-48", // Consistent bottom margin with responsive scaling
      className
    )}>
      {(title || subtitle) && (
        <div className={cn(
          "mb-12", // Consistent space between header and content
          centered ? "text-center" : ""
        )}>
          {title && (
            <h2 className="text-3xl md:text-4xl font-extrabold text-slate-800 dark:text-slate-100 tracking-tight">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="mt-4 text-lg text-slate-600 dark:text-slate-300 max-w-2xl leading-relaxed">
              {subtitle}
            </p>
          )}
        </div>
      )}
      {children}
    </section>
  )

  if (!animate) return content

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
    >
      {content}
    </motion.div>
  )
}