import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Code, Palette, Wrench, Users, BookOpen } from "lucide-react"

export default function Skills() {
  const technicalSkills = [
    { name: "HTML/CSS", level: 95, category: "Frontend" },
    { name: "JavaScript", level: 90, category: "Frontend" },
    { name: "React", level: 85, category: "Frontend" },
    { name: "TypeScript", level: 80, category: "Frontend" },
    { name: "Next.js", level: 75, category: "Frontend" },
    { name: "Tailwind CSS", level: 90, category: "Styling" },
    { name: "SCSS/Sass", level: 85, category: "Styling" },
    { name: "CSS-in-JS", level: 70, category: "Styling" },
    { name: "Git/GitHub", level: 85, category: "Tools" },
    { name: "VS Code", level: 90, category: "Too<PERSON>" },
    { name: "Figma", level: 75, category: "Tools" },
    { name: "npm/yarn", level: 85, category: "Tools" },
    { name: "Responsive Design", level: 90, category: "Other" },
    { name: "Web Performance", level: 75, category: "Other" },
    { name: "API Integration", level: 80, category: "Other" },
  ]

  const softSkills = [
    "Problem Solving",
    "Critical Thinking",
    "Communication",
    "Team Collaboration",
    "Time Management",
    "Attention to Detail",
    "Adaptability",
    "Learning Agility"
  ]

  const currentlyLearning = [
    "Advanced React Patterns",
    "Frontend Architecture",
    "Testing (Jest, Cypress)",
    "GraphQL",
    "Vue.js",
    "Node.js Basics"
  ]

  const getSkillsByCategory = (category: string) => {
    return technicalSkills.filter(skill => skill.category === category)
  }

  const categories = [
    { name: "Frontend", icon: Code, skills: getSkillsByCategory("Frontend") },
    { name: "Styling", icon: Palette, skills: getSkillsByCategory("Styling") },
    { name: "Tools", icon: Wrench, skills: getSkillsByCategory("Tools") },
    { name: "Other", icon: Users, skills: getSkillsByCategory("Other") },
  ]

  return (
    <div className="container mx-auto px-6 py-8 max-w-6xl">
      {/* Hero Section */}
      <div className="space-y-6 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold">Skills & Expertise</h1>
        <p className="text-xl text-muted-foreground">
          A comprehensive overview of my technical skills and professional capabilities
        </p>
      </div>

      {/* Technical Skills */}
      <div className="space-y-8 mb-12">
        <h2 className="text-2xl font-bold">Technical Skills</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {categories.map(({ name, icon: Icon, skills }) => (
            <Card key={name}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon className="w-5 h-5 text-primary" />
                  {name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {skills.map(({ name: skillName, level }) => (
                  <div key={skillName} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{skillName}</span>
                      <span className="text-muted-foreground">{level}%</span>
                    </div>
                    <Progress value={level} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Soft Skills */}
      <div className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-primary" />
              Soft Skills
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {softSkills.map((skill) => (
                <Badge key={skill} variant="secondary" className="justify-center py-2">
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Currently Learning */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-primary" />
            Currently Learning
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentlyLearning.map((skill) => (
              <div key={skill} className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="text-sm">{skill}</span>
              </div>
            ))}
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            I'm continuously expanding my skill set and staying up-to-date with the latest technologies 
            and best practices in frontend development.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}