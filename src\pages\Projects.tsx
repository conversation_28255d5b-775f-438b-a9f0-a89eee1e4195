import { ExternalLink, Gith<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function Projects() {
  const projects = [
    {
      id: 1,
      title: "E-commerce Platform",
      description: "A modern e-commerce platform built with React and TypeScript, featuring user authentication, shopping cart, and payment integration.",
      image: "/api/placeholder/600/400",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Node.js", "MongoDB"],
      liveUrl: "#",
      githubUrl: "#",
      status: "Completed"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      image: "/api/placeholder/600/400",
      technologies: ["Next.js", "React", "Prisma", "PostgreSQL", "Tailwind CSS"],
      liveUrl: "#",
      githubUrl: "#",
      status: "In Progress"
    },
    {
      id: 3,
      title: "Weather Dashboard",
      description: "A responsive weather dashboard that displays current weather conditions and forecasts using external weather APIs.",
      image: "/api/placeholder/600/400",
      technologies: ["React", "JavaScript", "CSS Modules", "Weather API"],
      liveUrl: "#",
      githubUrl: "#",
      status: "Completed"
    },
    {
      id: 4,
      title: "Portfolio Website",
      description: "Personal portfolio website showcasing my projects and skills, built with modern web technologies and responsive design.",
      image: "/api/placeholder/600/400",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      liveUrl: "#",
      githubUrl: "#",
      status: "Completed"
    }
  ]

  return (
    <div className="container mx-auto px-6 py-8 max-w-6xl">
      {/* Hero Section */}
      <div className="space-y-6 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold">My Projects</h1>
        <p className="text-xl text-muted-foreground">
          A showcase of my recent work and development projects
        </p>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {projects.map((project) => (
          <Card key={project.id} className="group hover:shadow-large transition-all duration-300">
            <div className="aspect-video bg-muted rounded-t-lg overflow-hidden">
              <div className="w-full h-full bg-gradient-subtle flex items-center justify-center">
                <div className="text-center space-y-2">
                  <div className="w-16 h-16 bg-primary/20 rounded-lg mx-auto flex items-center justify-center">
                    <Github className="w-8 h-8 text-primary" />
                  </div>
                  <p className="text-sm text-muted-foreground">Project Preview</p>
                </div>
              </div>
            </div>
            
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {project.title}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant={project.status === "Completed" ? "default" : "secondary"}>
                      {project.status}
                    </Badge>
                  </div>
                </div>
              </div>
              <CardDescription>{project.description}</CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Technologies */}
              <div className="flex flex-wrap gap-2">
                {project.technologies.map((tech) => (
                  <Badge key={tech} variant="outline" className="text-xs">
                    {tech}
                  </Badge>
                ))}
              </div>
              
              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button asChild variant="default" size="sm" className="flex-1">
                  <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Live Demo
                  </a>
                </Button>
                <Button asChild variant="outline" size="sm" className="flex-1">
                  <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                    <Github className="w-4 h-4 mr-2" />
                    Code
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Information */}
      <Card className="mt-12">
        <CardHeader>
          <CardTitle>More Projects Coming Soon</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            I'm constantly working on new projects and experimenting with different technologies. 
            Check back regularly to see my latest work, or connect with me on GitHub to follow my development journey.
          </p>
          <div className="flex gap-4 mt-4">
            <Button asChild variant="outline">
              <a href="https://github.com/cjjutba" target="_blank" rel="noopener noreferrer">
                <Github className="w-4 h-4 mr-2" />
                View All Repositories
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}